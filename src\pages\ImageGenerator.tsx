import { useState, useCallback, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { ImageUpload } from "@/components/ImageUpload";
import { ImageToolbar } from "@/components/ImageToolbar";
import { TextEditor, TextOptions } from "@/components/TextEditor";
import { CanvasEditor } from "@/components/CanvasEditor";
import { ProjectBrowser } from "@/components/ProjectBrowser";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/AuthContext";
import { useCampaigns } from "@/hooks/useCampaigns";
import { toast } from "sonner";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import NotebookHeader from "@/components/dashboard/NotebookHeader";

const ImageGenerator = () => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [projectTitle, setProjectTitle] = useState<string>("Untitled Project");
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [addTextToCanvas, setAddTextToCanvas] = useState<((textOptions: TextOptions) => void) | null>(null);
  const [updateTextOnCanvas, setUpdateTextOnCanvas] = useState<((textOptions: TextOptions) => void) | null>(null);
  const [deleteTextFromCanvas, setDeleteTextFromCanvas] = useState<(() => void) | null>(null);
  const [getCanvasData, setGetCanvasData] = useState<(() => any) | null>(null);
  const [selectedTextOptions, setSelectedTextOptions] = useState<TextOptions | null>(null);
  const [currentProject, setCurrentProject] = useState<any>({});
  const { user, loading, signOut } = useAuth();
  const { saveProject, saving, loadProject } = useCampaigns();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Handle project initialization from route state
  useEffect(() => {
    const state = location.state as any;
    if (state) {
      if (state.mode === "new" && state.projectTitle) {
        setProjectTitle(state.projectTitle);
        setCurrentProjectId(null);
        setImageUrl("");
        setImageFile(null);
        setCurrentProject({});
        // Clear the state to prevent re-initialization
        window.history.replaceState({}, document.title);
      } else if (state.mode === "existing" && state.project) {
        const project = state.project;
        setImageUrl(project.image_url);
        setProjectTitle(project.title);
        setCurrentProjectId(project.id);
        setImageFile(null);
        setCurrentProject({
          provider: project.provider || 'upload',
          author_name: project.author_name,
          author_username: project.author_username,
          author_profile_url: project.author_profile_url,
          image_source_id: project.image_source_id,
          image_source_url: project.image_source_url,
          image_source_tags: project.image_source_tags,
          image_bucket_path: project.image_bucket_path,
          downloaded_at: project.downloaded_at
        });
        setLoadedProjectData(project);
        // Clear the state to prevent re-initialization
        window.history.replaceState({}, document.title);
      }
    }
  }, [location.state]);

  const handleSetAddTextToCanvas = useCallback((fn: (textOptions: TextOptions) => void) => {
    setAddTextToCanvas(() => fn);
  }, []);

  const handleSetUpdateTextOnCanvas = useCallback((fn: (textOptions: TextOptions) => void) => {
    setUpdateTextOnCanvas(() => fn);
  }, []);

  const handleSetDeleteTextFromCanvas = useCallback((fn: () => void) => {
    setDeleteTextFromCanvas(() => fn);
  }, []);

  const handleTextSelected = useCallback((textOptions: TextOptions | null) => {
    setSelectedTextOptions(textOptions);
  }, []);

  const handleImageUpload = useCallback((url: string, file?: File, imageData?: {
    provider: string;
    authorName?: string;
    authorUsername?: string;
    authorProfileUrl?: string;
    imageSourceId?: string;
    imageSourceUrl?: string;
    imageSourceTags?: string[];
    imageBucketPath?: string;
    downloadedAt?: string;
  }) => {
    setImageUrl(url);
    setImageFile(file || null);
    
    // Store image data for saving
    if (imageData) {
      setCurrentProject(prev => ({
        ...prev,
        provider: imageData.provider,
        author_name: imageData.authorName,
        author_username: imageData.authorUsername,
        author_profile_url: imageData.authorProfileUrl,
        image_source_id: imageData.imageSourceId,
        image_source_url: imageData.imageSourceUrl,
        image_source_tags: imageData.imageSourceTags,
        image_bucket_path: imageData.imageBucketPath,
        downloaded_at: imageData.downloadedAt
      }));
    } else {
      // Reset for uploaded files
      setCurrentProject(prev => ({
        ...prev,
        provider: 'upload',
        author_name: undefined,
        author_username: undefined,
        author_profile_url: undefined,
        image_source_id: undefined,
        image_source_url: undefined,
        image_source_tags: undefined,
        image_bucket_path: undefined,
        downloaded_at: undefined
      }));
    }
  }, []);

  const handleAddText = useCallback((textOptions: TextOptions) => {
    if (addTextToCanvas && textOptions) {
      addTextToCanvas(textOptions);
    }
  }, [addTextToCanvas]);

  const handleUpdateText = useCallback((textOptions: TextOptions) => {
    if (updateTextOnCanvas && textOptions) {
      updateTextOnCanvas(textOptions);
    }
  }, [updateTextOnCanvas]);

  const handleDeleteText = useCallback(() => {
    if (deleteTextFromCanvas) {
      deleteTextFromCanvas();
    }
  }, [deleteTextFromCanvas]);

  const handleSetGetCanvasData = useCallback((fn: () => any) => {
    setGetCanvasData(() => fn);
  }, []);

  const handleSaveProject = useCallback(async () => {
    if (!getCanvasData) {
      toast.error("Canvas not ready");
      return;
    }

    const canvasData = getCanvasData();
    
    const project = {
      id: currentProjectId,
      title: projectTitle,
      image_url: imageUrl,
      image_bucket_path: currentProject.image_bucket_path || (currentProject.provider === 'pixabay' ? currentProject.image_bucket_path : (imageFile ? '' : '')),
      text_objects: canvasData.textObjects,
      canvas_settings: canvasData.canvasSettings,
      provider: currentProject.provider || 'upload',
      author_name: currentProject.author_name,
      author_username: currentProject.author_username,
      author_profile_url: currentProject.author_profile_url,
      image_source_id: currentProject.image_source_id,
      image_source_url: currentProject.image_source_url,
      image_source_tags: currentProject.image_source_tags,
      downloaded_at: currentProject.downloaded_at
    };

    const savedId = await saveProject(project, imageFile || undefined);
    if (savedId && !currentProjectId) {
      setCurrentProjectId(savedId);
    }
  }, [getCanvasData, currentProjectId, projectTitle, imageUrl, imageFile, saveProject]);

  const handleProjectLoad = useCallback(async (project: any) => {
    setImageUrl(project.image_url);
    setProjectTitle(project.title);
    setCurrentProjectId(project.id);
    setImageFile(null); // Clear file since we're loading from URL
    
    // Set project metadata including all audit data
    setCurrentProject({
      provider: project.provider || 'upload',
      author_name: project.author_name,
      author_username: project.author_username,
      author_profile_url: project.author_profile_url,
      image_source_id: project.image_source_id,
      image_source_url: project.image_source_url,
      image_source_tags: project.image_source_tags,
      image_bucket_path: project.image_bucket_path,
      downloaded_at: project.downloaded_at
    });
    
    // Store project data to be loaded into canvas
    setLoadedProjectData(project);
  }, []);

  // Add state for loaded project data
  const [loadedProjectData, setLoadedProjectData] = useState<any>(null);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
       <NotebookHeader title={projectTitle} notebookId={currentProjectId} />
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1 min-w-0">
             
              <div className="flex items-center gap-2 lg:gap-4 mt-2 flex-wrap">
                <Input
                  value={projectTitle}
                  onChange={(e) => setProjectTitle(e.target.value)}
                  className="max-w-[200px] lg:max-w-xs"
                  placeholder="Project title"
                />
                <ProjectBrowser onProjectLoad={handleProjectLoad} />
                {imageUrl && (
                  <Button 
                    onClick={handleSaveProject}
                    disabled={saving}
                    className="bg-primary hover:bg-primary/90"
                    size="sm"
                  >
                    {saving ? "Saving..." : "Save"}
                  </Button>
                )}
              </div>
            </div>

            {/* Image Toolbar - Always visible on tablets and larger */}
            <div className="hidden md:block">
              <ImageToolbar onImageUpload={handleImageUpload} compact />
            </div>
          </div>

          {/* Mobile Image Toolbar - Below header on small screens */}
          <div className="md:hidden mt-4 pt-4 border-t border-border">
            <ImageToolbar onImageUpload={handleImageUpload} compact />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - Tools */}
          <div className="lg:col-span-1 space-y-6">
            {/* Always show ImageToolbar in sidebar on desktop for easy image changes */}
            <div className="hidden lg:block animate-fade-in">
              <ImageToolbar onImageUpload={handleImageUpload} />
            </div>
            
            {!imageUrl && (
              <div className="lg:hidden animate-fade-in">
                <ImageUpload onImageUpload={handleImageUpload} />
              </div>
            )}
            
            {imageUrl && (
              <div className="animate-slide-in">
                <TextEditor
                  onAddText={handleAddText}
                  onUpdateText={handleUpdateText}
                  onDeleteText={handleDeleteText}
                  selectedTextOptions={selectedTextOptions}
                />
              </div>
            )}
          </div>

          {/* Main Canvas Area */}
          <div className="lg:col-span-2 space-y-4">
            <div className="animate-fade-in">
                <CanvasEditor
                  imageUrl={imageUrl}
                  onAddText={handleSetAddTextToCanvas}
                  onUpdateText={handleSetUpdateTextOnCanvas}
                  onDeleteText={handleSetDeleteTextFromCanvas}
                  onTextSelected={handleTextSelected}
                  onGetCanvasData={handleSetGetCanvasData}
                  loadedProjectData={loadedProjectData}
                  onProjectDataLoaded={() => setLoadedProjectData(null)}
                />
            </div>
            
            {/* Unsplash Attribution */}
            {currentProject.provider === 'unsplash' && currentProject.author_name && (
              <div className="text-xs text-muted-foreground bg-card/50 p-3 rounded-lg border">
                Photo by{' '}
                <a
                  href={currentProject.author_profile_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline hover:text-foreground"
                >
                  {currentProject.author_name}
                </a>{' '}
                on{' '}
                <a
                  href="https://unsplash.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline hover:text-foreground"
                >
                  Unsplash
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        {!imageUrl && (
          <div className="mt-12 text-center space-y-4 animate-fade-in">
            <h2 className="text-xl font-semibold text-foreground">
              How to use Text Overlay Editor
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  1
                </div>
                <h3 className="font-semibold">Upload Image</h3>
                <p className="text-muted-foreground text-sm">
                  Drag and drop or click to upload your image
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  2
                </div>
                <h3 className="font-semibold">Customize Text</h3>
                <p className="text-muted-foreground text-sm">
                  Add text with custom fonts, colors, and effects
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  3
                </div>
                <h3 className="font-semibold">Export</h3>
                <p className="text-muted-foreground text-sm">
                  Download your image with text overlays
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageGenerator;