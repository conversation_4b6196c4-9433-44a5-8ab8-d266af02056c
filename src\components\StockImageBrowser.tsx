import React, { useState, useEffect } from 'react';
import { Search, Download, User, ExternalLink, Image, Eye, Loader2, RectangleHorizontal, RectangleVertical, Square, Grid3X3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface StockImage {
  id: string;
  urls: {
    small: string;
    regular: string;
    full: string;
  };
  alt_description: string;
  user: {
    name: string;
    username: string;
    profile_url: string;
  };
  download_url: string;
  width: number;
  height: number;
  tags?: string[];
  source_url?: string;
  views?: number;
  downloads?: number;
}

interface StockImageBrowserProps {
  onImageSelect: (imageUrl: string, imageData: {
    provider: string;
    authorName?: string;
    authorUsername?: string;
    authorProfileUrl?: string;
    imageSourceId?: string;
    imageSourceUrl?: string;
    imageSourceTags?: string[];
    imageBucketPath?: string;
    downloadedAt?: string;
  }) => void;
  onClose: () => void;
}

export const StockImageBrowser: React.FC<StockImageBrowserProps> = ({
  onImageSelect,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState('pixabay');
  const [searchQuery, setSearchQuery] = useState('');
  const [orientation, setOrientation] = useState('all');
  const [images, setImages] = useState<StockImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedImage, setSelectedImage] = useState<StockImage | null>(null);

  const searchImages = async (query: string, provider: string, page: number = 1, reset: boolean = true) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const functionName = provider === 'unsplash' ? 'unsplash-search' : 'pixabay-search';
      const { data, error } = await supabase.functions.invoke(functionName, {
        body: {
          query: query.trim(),
          page,
          perPage: 20,
          orientation: orientation === 'all' ? undefined : orientation,
        },
      });

      if (error) {
        console.error('Error searching images:', error);
        toast.error(`Failed to search ${provider} images`);
        return;
      }

      if (reset) {
        setImages(data.images);
      } else {
        setImages(prev => [...prev, ...data.images]);
      }
      
      setTotalPages(data.totalPages);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error searching images:', error);
      toast.error(`Failed to search ${provider} images`);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setCurrentPage(1);
      setSelectedImage(null);
      searchImages(searchQuery, activeTab, 1, true);
    }
  };

  const loadMore = () => {
    if (currentPage < totalPages && !loading) {
      searchImages(searchQuery, activeTab, currentPage + 1, false);
    }
  };

  const handleImageSelect = (image: StockImage) => {
    setSelectedImage(image);
  };

  const handlePixabayDownload = async (image: StockImage) => {
    setDownloading(true);
    try {
      const { data, error } = await supabase.functions.invoke('pixabay-download', {
        body: {
          imageUrl: image.download_url,
          imageId: image.id,
          tags: image.tags,
          sourceUrl: image.source_url
        }
      });

      if (error) {
        console.error('Error downloading Pixabay image:', error);
        toast.error('Failed to download image');
        return;
      }

      onImageSelect(data.imageUrl, {
        provider: 'pixabay',
        imageSourceId: data.imageSourceId,
        imageSourceUrl: data.imageSourceUrl,
        imageSourceTags: data.imageSourceTags,
        imageBucketPath: data.bucketPath,
        downloadedAt: data.downloadedAt
      });
      onClose();
      toast.success('Pixabay image downloaded and ready to use!');
    } catch (error) {
      console.error('Error downloading Pixabay image:', error);
      toast.error('Failed to download image');
    } finally {
      setDownloading(false);
    }
  };

  const confirmSelection = () => {
    if (selectedImage) {
      if (activeTab === 'unsplash') {
        onImageSelect(selectedImage.urls.regular, {
          provider: 'unsplash',
          authorName: selectedImage.user.name,
          authorUsername: selectedImage.user.username,
          authorProfileUrl: selectedImage.user.profile_url
        });
        onClose();
        toast.success('Unsplash image selected!');
      } else {
        handlePixabayDownload(selectedImage);
      }
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setImages([]);
    setSelectedImage(null);
    setCurrentPage(1);
    setTotalPages(0);
    
    // Auto-search popular images when switching tabs
    if (searchQuery.trim()) {
      searchImages(searchQuery, value, 1, true);
    } else {
      searchImages('popular', value, 1, true);
    }
  };

  // Load popular images on mount
  useEffect(() => {
    searchImages('popular', activeTab, 1, true);
  }, []);

  // Search when orientation changes
  useEffect(() => {
    if (searchQuery.trim()) {
      searchImages(searchQuery, activeTab, 1, true);
    }
  }, [orientation]);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-lg w-full max-w-6xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Browse Stock Images</h2>
            <Button variant="ghost" onClick={onClose} size="sm">
              ✕
            </Button>
          </div>
          
          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-4">
            <TabsList className="grid w-full grid-cols-2">
               <TabsTrigger value="pixabay" className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Pixabay
              </TabsTrigger>
              <TabsTrigger value="unsplash" className="flex items-center gap-2">
                <Image className="w-4 h-4" />
                Unsplash
              </TabsTrigger>
             
            </TabsList>
          </Tabs>
          
          {/* Search Form */}
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search for images (e.g., cinema, city, nature...)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={orientation} onValueChange={setOrientation}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Grid3X3 className="w-4 h-4" />
                    All
                  </div>
                </SelectItem>
                <SelectItem value="landscape">
                  <div className="flex items-center gap-2">
                    <RectangleHorizontal className="w-4 h-4" />
                    Landscape
                  </div>
                </SelectItem>
                <SelectItem value="portrait">
                  <div className="flex items-center gap-2">
                    <RectangleVertical className="w-4 h-4" />
                    Portrait
                  </div>
                </SelectItem>
                <SelectItem value="squarish">
                  <div className="flex items-center gap-2">
                    <Square className="w-4 h-4" />
                    Square
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <Button type="submit" disabled={loading}>
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </form>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Image Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {images.map((image) => (
                <Card 
                  key={image.id} 
                  className={`cursor-pointer transition-all hover:ring-2 hover:ring-primary ${
                    selectedImage?.id === image.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => handleImageSelect(image)}
                >
                  <CardContent className="p-0">
                    <div className="aspect-square overflow-hidden rounded-t-lg">
                      <img
                        src={image.urls.small}
                        alt={image.alt_description}
                        className="w-full h-full object-cover transition-transform hover:scale-105"
                      />
                    </div>
                    <div className="p-2">
                      <p className="text-xs text-muted-foreground truncate">
                        by {image.user.name}
                      </p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{image.width} × {image.height}</span>
                        <div className="flex items-center gap-1">
                          {image.width > image.height ? (
                            <RectangleHorizontal className="w-3 h-3" />
                          ) : image.height > image.width ? (
                            <RectangleVertical className="w-3 h-3" />
                          ) : (
                            <Square className="w-3 h-3" />
                          )}
                        </div>
                      </div>
                      {activeTab === 'pixabay' && (
                        <div className="flex gap-2 mt-1">
                          {image.views && (
                            <Badge variant="secondary" className="text-xs">
                              {image.views} views
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More */}
            {currentPage < totalPages && (
              <div className="text-center mt-6">
                <Button onClick={loadMore} disabled={loading} variant="outline">
                  {loading ? 'Loading...' : 'Load More'}
                </Button>
              </div>
            )}

            {images.length === 0 && !loading && (
              <div className="text-center py-12 text-muted-foreground">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Search for images to get started</p>
                <p className="text-sm">Try terms like "cinema", "city", "nature", or "business"</p>
              </div>
            )}
          </div>

          {/* Preview Panel */}
          {selectedImage && (
            <div className="w-80 border-l border-border p-6 overflow-y-auto">
              <div className="space-y-4">
                <img
                  src={selectedImage.urls.regular}
                  alt={selectedImage.alt_description}
                  className="w-full rounded-lg"
                />
                
                <div className="space-y-2">
                  <h3 className="font-medium">Image Details</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedImage.alt_description}
                  </p>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <User className="w-4 h-4" />
                    <span>Photo by {selectedImage.user.name}</span>
                    <Button variant="ghost" size="sm" asChild>
                      <a
                        href={selectedImage.user.profile_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </Button>
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline">
                      {selectedImage.width} × {selectedImage.height}
                    </Badge>
                    {activeTab === 'pixabay' && selectedImage.views && (
                      <Badge variant="outline">
                        {selectedImage.views} views
                      </Badge>
                    )}
                    {activeTab === 'pixabay' && selectedImage.downloads && (
                      <Badge variant="outline">
                        {selectedImage.downloads} downloads
                      </Badge>
                    )}
                  </div>

                  {selectedImage.tags && selectedImage.tags.length > 0 && (
                    <div className="space-y-1">
                      <p className="text-xs font-medium">Tags:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedImage.tags.slice(0, 6).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Button 
                    onClick={confirmSelection} 
                    className="w-full"
                    disabled={downloading}
                  >
                    {downloading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        {activeTab === 'pixabay' ? 'Download & Use' : 'Use This Image'}
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    {activeTab === 'unsplash' ? (
                      <>
                        By Unsplash • Free to use under the{' '}
                        <a
                          href="https://unsplash.com/license"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          Unsplash License
                        </a>
                      </>
                    ) : (
                      <>
                        By Pixabay • Free for commercial use •{' '}
                        <a
                          href="https://pixabay.com/service/license/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          License
                        </a>
                      </>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};