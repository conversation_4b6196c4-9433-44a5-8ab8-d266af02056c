import { useState } from "react";
import { Upload, Camera, Sparkles, Plus, Image as ImageIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { Stock<PERSON>mageBrowser } from "./StockImageBrowser";
import { AIImageGenerator } from "./AIImageGenerator";

interface ImageToolbarProps {
  onImageUpload: (imageUrl: string, file?: File, imageData?: {
    provider: string;
    authorName?: string;
    authorUsername?: string;
    authorProfileUrl?: string;
    imageSourceId?: string;
    imageSourceUrl?: string;
    imageSourceTags?: string[];
    imageBucketPath?: string;
    downloadedAt?: string;
  }) => void;
  compact?: boolean;
}

export const ImageToolbar = ({ onImageUpload, compact = false }: ImageToolbarProps) => {
  const [showStockBrowser, setShowStockBrowser] = useState(false);
  const [showAIGenerator, setShowAIGenerator] = useState(false);

  const handleFileUpload = (file: File) => {
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload an image file");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        onImageUpload(e.target.result as string, file);
        toast.success("Image uploaded successfully!");
      }
    };
    reader.readAsDataURL(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  if (compact) {
    return (
      <div className="flex flex-wrap gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() => document.getElementById("toolbar-file-input")?.click()}
          className="flex-1 min-w-0"
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload
        </Button>
        
        <Button
          size="sm"
          variant="outline"
          onClick={() => setShowStockBrowser(true)}
          className="flex-1 min-w-0"
        >
          <Camera className="h-4 w-4 mr-2" />
          Stock
        </Button>
        
        <Button
          size="sm"
          variant="outline"
          onClick={() => setShowAIGenerator(true)}
          className="flex-1 min-w-0"
        >
          <Sparkles className="h-4 w-4 mr-2" />
          AI
        </Button>

        <input
          id="toolbar-file-input"
          type="file"
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />

        {/* Modals */}
        {showStockBrowser && (
          <StockImageBrowser
            onImageSelect={(imageUrl, unsplashData) => {
              onImageUpload(imageUrl, undefined, unsplashData);
              toast.success("Stock image selected!");
            }}
            onClose={() => setShowStockBrowser(false)}
          />
        )}

        {showAIGenerator && (
          <AIImageGenerator
            onImageGenerated={(imageUrl, metadata) => {
              onImageUpload(imageUrl, undefined, {
                provider: metadata.provider,
                authorName: undefined,
                authorUsername: undefined,
                authorProfileUrl: undefined,
                imageSourceId: metadata.prompt,
                imageSourceUrl: undefined,
                imageSourceTags: [metadata.model, metadata.style, metadata.quality],
                imageBucketPath: undefined,
                downloadedAt: metadata.generatedAt
              });
              toast.success("AI image generated successfully!");
            }}
            onClose={() => setShowAIGenerator(false)}
          />
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium text-sm">Image Source</span>
          </div>
          
          <div className="grid grid-cols-1 gap-2">
            <Button
              variant="outline"
              onClick={() => document.getElementById("toolbar-file-input")?.click()}
              className="justify-start"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setShowStockBrowser(true)}
              className="justify-start"
            >
              <Camera className="h-4 w-4 mr-2" />
              Browse Stock
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setShowAIGenerator(true)}
              className="justify-start"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Generate AI
            </Button>
          </div>
        </div>

        <input
          id="toolbar-file-input"
          type="file"
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />

        {/* Modals */}
        {showStockBrowser && (
          <StockImageBrowser
            onImageSelect={(imageUrl, unsplashData) => {
              onImageUpload(imageUrl, undefined, unsplashData);
              toast.success("Stock image selected!");
            }}
            onClose={() => setShowStockBrowser(false)}
          />
        )}

        {showAIGenerator && (
          <AIImageGenerator
            onImageGenerated={(imageUrl, metadata) => {
              onImageUpload(imageUrl, undefined, {
                provider: metadata.provider,
                authorName: undefined,
                authorUsername: undefined,
                authorProfileUrl: undefined,
                imageSourceId: metadata.prompt,
                imageSourceUrl: undefined,
                imageSourceTags: [metadata.model, metadata.style, metadata.quality],
                imageBucketPath: undefined,
                downloadedAt: metadata.generatedAt
              });
              toast.success("AI image generated successfully!");
            }}
            onClose={() => setShowAIGenerator(false)}
          />
        )}
      </CardContent>
    </Card>
  );
};